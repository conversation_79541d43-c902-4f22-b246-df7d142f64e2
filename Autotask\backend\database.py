"""
Legacy database module - now uses the unified connection manager.
This file is kept for backward compatibility but delegates to the new singleton connection manager.
"""

import sys
import os
from typing import List, Dict, Optional

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
from src.database import get_db_connection

def connect_snowflake():
    """Legacy function - now uses unified connection manager."""
    db = get_db_connection()
    return db.connect()

def close_snowflake():
    """Legacy function - now uses unified connection manager."""
    db = get_db_connection()
    db.close()

def execute_query(query: str, params: Optional[tuple] = None) -> List[Dict]:
    """
    Legacy function - now uses unified connection manager.
    Executes a SQL query on Snowflake and returns the results as a list of dictionaries.
    """
    db = get_db_connection()
    return db.execute_query(query, params)