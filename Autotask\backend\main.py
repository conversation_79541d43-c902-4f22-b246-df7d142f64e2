from fastapi import FastAPI
from .routers import tickets, technicians
import sys
import os

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
from src.database import get_db_connection

app = FastAPI()

@app.on_event("startup")
def startup_event():
    """Initialize the database connection on startup."""
    db = get_db_connection()
    db.connect()

@app.on_event("shutdown")
def shutdown_event():
    """Close the database connection on shutdown."""
    db = get_db_connection()
    db.close()

app.include_router(tickets.router)
app.include_router(technicians.router)

@app.get("/")
def read_root():
    return {"message": "FastAPI backend is running!"} 