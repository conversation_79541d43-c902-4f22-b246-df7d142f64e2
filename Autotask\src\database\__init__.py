"""
Database Package
Contains database connection and operations.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.database.connection_manager import SnowflakeConnectionManager, get_db_connection

# Legacy import - deprecated, use get_db_connection() instead
from src.database.snowflake_db import SnowflakeConnection

__all__ = ['SnowflakeConnectionManager', 'get_db_connection']
