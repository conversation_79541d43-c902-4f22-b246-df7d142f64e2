"""
Unified Snowflake Database Connection Manager
Singleton class that provides a single persistent connection for the entire application.
Combines features from both backend/database.py and src/database/snowflake_db.py.
"""

import os
import snowflake.connector
import pandas as pd
import re
import json
from typing import List, Dict, Optional, Any
from dotenv import load_dotenv
import threading
import time

# Load environment variables
load_dotenv()


class SnowflakeConnectionManager:
    """
    Singleton class for managing a single persistent Snowflake database connection.
    Combines SSL configuration, error handling, and comprehensive database operations.
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(SnowflakeConnectionManager, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        # Connection parameters from environment
        self.sf_account = os.getenv('SF_ACCOUNT')
        self.sf_user = os.getenv('SF_USER')
        self.sf_password = os.getenv('SF_PASSWORD')
        self.sf_warehouse = os.getenv('SF_WAREHOUSE')
        self.sf_database = os.getenv('SF_DATABASE')
        self.sf_schema = os.getenv('SF_SCHEMA')
        self.sf_role = os.getenv('SF_ROLE')
        self.sf_passcode = os.getenv('SF_PASSCODE')
        
        # Connection state
        self.conn = None
        self._connection_lock = threading.Lock()
        self._initialized = True
        
        # Initialize connection
        self.connect()
    
    def connect(self) -> bool:
        """
        Establishes a connection to Snowflake with SSL configuration.
        Returns True if successful, False otherwise.
        """
        with self._connection_lock:
            if self.conn is not None and not self.conn.is_closed():
                return True
                
            try:
                self.conn = snowflake.connector.connect(
                    user=self.sf_user,
                    password=self.sf_password,
                    account=self.sf_account,
                    warehouse=self.sf_warehouse,
                    database=self.sf_database,
                    schema=self.sf_schema,
                    role=self.sf_role,
                    passcode=self.sf_passcode,
                    # SSL configuration to handle certificate issues
                    validate_default_parameters=False,
                    # Disable OCSP check which often causes certificate issues
                    disable_request_pooling=True,
                    # Additional SSL settings to handle certificate validation issues
                    insecure_mode=True,  # Disable SSL certificate validation
                    ocsp_fail_open=True,  # Allow connection even if OCSP check fails
                )
                print("Successfully connected to Snowflake!")
                return True
            except Exception as e:
                print(f"Failed to connect to Snowflake: {e}")
                print("Continuing without Snowflake connection for testing...")
                self.conn = None
                return False
    
    def close(self):
        """Close the Snowflake connection."""
        with self._connection_lock:
            if self.conn is not None:
                try:
                    self.conn.close()
                    print("Snowflake connection closed.")
                except Exception as e:
                    print(f"Error closing Snowflake connection: {e}")
                finally:
                    self.conn = None
    
    def is_connected(self) -> bool:
        """Check if the connection is active."""
        return self.conn is not None and not self.conn.is_closed()
    
    def ensure_connection(self) -> bool:
        """Ensure connection is active, reconnect if necessary."""
        if not self.is_connected():
            return self.connect()
        return True
    
    def execute_query(self, query: str, params: Optional[tuple] = None) -> List[Dict]:
        """
        Executes a SQL query on Snowflake and returns the results as a list of dictionaries.
        Falls back to mock data if Snowflake connection fails.
        """
        print(f"DEBUG: execute_query called with query: {query[:100]}...")
        print(f"DEBUG: execute_query params: {params}")

        if not self.ensure_connection():
            print("WARNING: No Snowflake connection available. Using mock data for demonstration.")
            return self._get_mock_data(query)

        results = []
        try:
            print("DEBUG: Executing query on Snowflake...")
            with self.conn.cursor(snowflake.connector.DictCursor) as cur:
                cur.execute(query, params)
                results = cur.fetchall()
                print(f"DEBUG: Query executed successfully, got {len(results)} results")
                if results:
                    print(f"DEBUG: First result: {results[0]}")
        except Exception as e:
            print(f"ERROR: Failed to execute Snowflake query: {e}")
            print(f"DEBUG: Query was: {query}")
            print(f"DEBUG: Params were: {params}")
            import traceback
            traceback.print_exc()
            raise e

        print(f"DEBUG: execute_query returning {len(results)} results")
        return results
    
    def _get_mock_data(self, query: str) -> List[Dict]:
        """
        Returns mock data for testing when Snowflake connection is not available.
        """
        query_lower = query.lower()
        
        if "company_4130_data" in query_lower and "select" in query_lower:
            # Mock ticket data
            return [
                {
                    "TICKETNUMBER": "T240001",
                    "TITLE": "Email not working",
                    "DESCRIPTION": "Cannot send emails from Outlook",
                    "ISSUETYPE": "1",
                    "SUBISSUETYPE": "2", 
                    "TICKETCATEGORY": "3",
                    "PRIORITY": "2",
                    "STATUS": "1",
                    "DUEDATETIME": "2024-01-15 10:00:00",
                    "CREATEDATE": "2024-01-10 09:00:00"
                },
                {
                    "TICKETNUMBER": "T240002", 
                    "TITLE": "Printer offline",
                    "DESCRIPTION": "Office printer showing offline status",
                    "ISSUETYPE": "2",
                    "SUBISSUETYPE": "1",
                    "TICKETCATEGORY": "1", 
                    "PRIORITY": "3",
                    "STATUS": "2",
                    "DUEDATETIME": "2024-01-16 14:00:00",
                    "CREATEDATE": "2024-01-11 11:30:00"
                }
            ]
        elif "technician_dummy_data" in query_lower:
            # Mock technician data
            return [
                {
                    "TECHNICIAN_ID": "TECH001",
                    "NAME": "John Smith",
                    "EMAIL": "<EMAIL>",
                    "ROLE": "Senior Technician",
                    "SKILLS": "Hardware, Software, Network",
                    "AVAILABILITY_STATUS": "Available",
                    "CURRENT_WORKLOAD": "3",
                    "SPECIALIZATIONS": "Windows, Office 365"
                }
            ]
        else:
            return []

    def call_cortex_llm(self, prompt: str, model: str = 'mixtral-8x7b', expect_json: bool = True) -> Optional[Any]:
        """
        Calls Snowflake Cortex LLM with the given prompt.

        Args:
            prompt (str): The prompt to send to the LLM
            model (str): The model to use (default: mixtral-8x7b)
            expect_json (bool): Whether to expect JSON response

        Returns:
            dict or str: LLM response, parsed as JSON if expect_json=True
        """
        if not self.ensure_connection():
            print("No Snowflake connection available for LLM call")
            return None

        try:
            with self.conn.cursor() as cur:
                # Escape single quotes in the prompt
                escaped_prompt = prompt.replace("'", "''")

                # Construct the SQL query to call Cortex LLM
                sql_query = f"SELECT SNOWFLAKE.CORTEX.COMPLETE('{model}', '{escaped_prompt}') AS response"

                print(f"Calling Cortex LLM with model: {model}")
                cur.execute(sql_query)
                result = cur.fetchone()

                if result and result[0]:
                    response_text = result[0]
                    print(f"LLM Response received: {response_text[:200]}...")

                    if expect_json:
                        try:
                            # Try to parse as JSON
                            return json.loads(response_text)
                        except json.JSONDecodeError:
                            print("Failed to parse LLM response as JSON, returning as string")
                            return response_text
                    else:
                        return response_text
                else:
                    print("No response from LLM")
                    return None

        except Exception as e:
            print(f"Error calling Cortex LLM: {e}")
            import traceback
            traceback.print_exc()
            return None

    def find_similar_tickets(self, search_conditions: List[str], params: List[str]) -> List[Dict]:
        """
        Searches for similar tickets based on provided conditions.

        Args:
            search_conditions (list): List of SQL WHERE conditions
            params (list): List of parameters for the conditions

        Returns:
            list: List of similar tickets
        """
        where_clause = ""
        if search_conditions:
            where_clause = "WHERE " + " OR ".join(search_conditions)

        query = f"""
        SELECT
            TITLE,
            DESCRIPTION,
            ISSUETYPE,
            SUBISSUETYPE,
            TICKETCATEGORY,
            TICKETTYPE,
            PRIORITY,
            STATUS
        FROM TEST_DB.PUBLIC.COMPANY_4130_DATA
        {where_clause}
        LIMIT 50;
        """
        print(f"Searching for similar tickets...")
        return self.execute_query(query, tuple(params))

    def get_ticket_data_for_similarity(self, ticket_ids: List[str]) -> List[Dict]:
        """
        Retrieves ticket data for similarity analysis.

        Args:
            ticket_ids (list): List of ticket IDs to retrieve

        Returns:
            list: List of ticket data dictionaries
        """
        if not ticket_ids:
            return []

        placeholders = ','.join(['%s'] * len(ticket_ids))
        query = f"""
        SELECT TICKETNUMBER, TITLE, DESCRIPTION, ISSUETYPE, SUBISSUETYPE,
               TICKETCATEGORY, PRIORITY, STATUS
        FROM TEST_DB.PUBLIC.COMPANY_4130_DATA
        WHERE TICKETNUMBER IN ({placeholders})
        """
        return self.execute_query(query, tuple(ticket_ids))

    def get_reference_data_from_db(self) -> Dict:
        """
        Retrieves reference data from the database for field mappings.

        Returns:
            dict: Reference data mapping IDs to labels
        """
        # This would typically query reference tables in the database
        # For now, return empty dict as reference data is loaded from file
        return {}


# Global instance getter
def get_db_connection() -> SnowflakeConnectionManager:
    """Get the singleton database connection instance."""
    return SnowflakeConnectionManager()
